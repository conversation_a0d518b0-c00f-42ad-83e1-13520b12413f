#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试周期对比功能
================

这个脚本用于测试新增的周期对比功能，验证：
1. 周期对比数据的计算是否正确
2. 图表描述信息是否包含对比内容
3. 消息发送格式是否符合要求

使用方法：
python 测试周期对比功能.py
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入资金曲线分析器
from 待复用模块.独立资金曲线分析器_企业微信版_自定义 import (
    FundCurveManager, 
    AnalysisConfig,
    WeChatConfig,
    FundCurveAnalyzer
)

def create_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    # 创建测试目录
    test_dir = project_root / "测试数据" / "周期对比测试"
    test_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建测试策略目录
    strategy_dir = test_dir / "data" / "子策略回测结果" / "测试策略_周期对比"
    strategy_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成测试资金曲线数据
    start_time = datetime.now() - timedelta(days=30)
    time_points = []
    equity_values = []
    
    # 生成720个小时的数据点（30天）
    base_equity = 1.0
    for i in range(720):
        current_time = start_time + timedelta(hours=i)
        time_points.append(current_time)
        
        # 模拟资金曲线波动
        # 总体上升趋势，但有波动和回撤
        trend = 0.0001 * i  # 总体上升趋势
        noise = np.random.normal(0, 0.002)  # 随机波动
        
        # 在某些时间段添加回撤
        if 200 <= i <= 250:  # 第200-250小时有回撤
            trend -= 0.0005
        elif 400 <= i <= 420:  # 第400-420小时有小回撤
            trend -= 0.0002
        
        equity_value = base_equity * (1 + trend + noise)
        equity_values.append(max(equity_value, 0.5))  # 确保净值不会太低
    
    # 创建DataFrame
    df = pd.DataFrame({
        'candle_begin_time': time_points,
        '净值': equity_values
    })
    
    # 保存到CSV文件
    csv_file = strategy_dir / "资金曲线.csv"
    df.to_csv(csv_file, index=False)
    
    print(f"测试数据已创建: {csv_file}")
    print(f"数据点数: {len(df)}")
    print(f"时间范围: {df['candle_begin_time'].min()} 到 {df['candle_begin_time'].max()}")
    print(f"净值范围: {df['净值'].min():.4f} 到 {df['净值'].max():.4f}")
    
    return str(test_dir)

def test_period_comparison():
    """测试周期对比功能"""
    print("\n" + "="*50)
    print("开始测试周期对比功能")
    print("="*50)
    
    # 创建测试数据
    test_data_dir = create_test_data()
    
    # 配置分析器
    analysis_config = AnalysisConfig(
        data_dir=test_data_dir,
        results_subdir="子策略回测结果",
        enable_charts=False,  # 暂时禁用图表生成，专注测试对比功能
        min_data_points=10
    )
    
    # 创建资金曲线管理器
    fund_manager = FundCurveManager(
        data_dir=test_data_dir,
        results_subdir="子策略回测结果",
        config=analysis_config
    )
    
    # 获取策略数据
    strategies = fund_manager.get_available_strategies()
    if not strategies['success'] or not strategies['strategies']:
        print("❌ 未找到测试策略数据")
        return False
    
    strategy = strategies['strategies'][0]
    print(f"\n测试策略: {strategy['name']}")
    
    # 获取策略数据和周期对比信息
    strategy_data = fund_manager.get_strategy_data(strategy['file_path'])
    
    if not strategy_data['success']:
        print(f"❌ 获取策略数据失败: {strategy_data['message']}")
        return False
    
    statistics = strategy_data['statistics']
    period_comparison = statistics.get('周期对比', {})
    
    print(f"\n📊 基础统计信息:")
    print(f"总收益率: {statistics.get('总收益率', 0):.2f}%")
    print(f"最大回撤: {statistics.get('最大回撤', 0):.2f}%")
    print(f"当前回撤: {statistics.get('当前回撤', 0):.2f}%")
    print(f"数据点数: {statistics.get('数据点数', 0)}")
    
    print(f"\n🔄 周期对比信息:")
    if period_comparison.get('has_comparison', False):
        print("✅ 周期对比数据可用")
        
        current_period = period_comparison.get('current_period', {})
        previous_period = period_comparison.get('previous_period', {})
        changes = period_comparison.get('changes', {})
        comparison_summary = period_comparison.get('comparison_summary', {})
        
        print(f"\n当前周期:")
        print(f"  时间: {current_period.get('time', 'N/A')}")
        print(f"  总收益率: {current_period.get('total_return', 0):.2f}%")
        print(f"  当前回撤: {current_period.get('drawdown', 0):.2f}%")
        print(f"  净值: {current_period.get('equity', 0):.4f}")
        
        print(f"\n上一周期:")
        print(f"  时间: {previous_period.get('time', 'N/A')}")
        print(f"  总收益率: {previous_period.get('total_return', 0):.2f}%")
        print(f"  当前回撤: {previous_period.get('drawdown', 0):.2f}%")
        print(f"  净值: {previous_period.get('equity', 0):.4f}")
        
        print(f"\n变化情况:")
        print(f"  收益率变化: {changes.get('return_change', 0):+.2f}% ({comparison_summary.get('return_trend', '持平')})")
        print(f"  回撤变化: {changes.get('drawdown_change', 0):+.2f}% ({comparison_summary.get('drawdown_trend', '持平')})")
        print(f"  时间间隔: {changes.get('time_interval_hours', 0):.1f}小时")
        
        # 测试图表描述格式
        print(f"\n📈 资金曲线图描述格式测试:")
        equity_description = f"📈 **{strategy['name']} - 资金曲线图**\n\n" + \
                           f"总收益率: {statistics.get('总收益率', 0):.2f}%\n" + \
                           f"年化收益率: {statistics.get('年化收益率', 0):.2f}%\n" + \
                           f"最大回撤: {statistics.get('最大回撤', 0):.2f}%"
        
        return_change = changes.get('return_change', 0)
        return_trend = comparison_summary.get('return_trend', '持平')
        if return_change != 0:
            change_symbol = "📈" if return_change > 0 else "📉"
            equity_description += f"\n\n**与上一周期对比:**\n" + \
                                f"{change_symbol} 收益率变化: {return_change:+.2f}% ({return_trend})"
        
        print(equity_description)
        
        print(f"\n📉 回撤分析图描述格式测试:")
        drawdown_description = f"📉 **{strategy['name']} - 回撤分析图**\n\n" + \
                             f"最大回撤: {statistics.get('最大回撤', 0):.2f}%\n" + \
                             f"当前回撤: {statistics.get('当前回撤', 0):.2f}%"
        
        drawdown_change = changes.get('drawdown_change', 0)
        drawdown_trend = comparison_summary.get('drawdown_trend', '持平')
        if drawdown_change != 0:
            change_symbol = "⚠️" if drawdown_change > 0 else "✅"
            drawdown_description += f"\n\n**与上一周期对比:**\n" + \
                                   f"{change_symbol} 回撤变化: {drawdown_change:+.2f}% ({drawdown_trend})"
        
        print(drawdown_description)
        
        print("\n✅ 周期对比功能测试通过！")
        return True
        
    else:
        print("❌ 周期对比数据不可用")
        print(f"原因: {period_comparison.get('message', '未知')}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    test_dir = project_root / "测试数据" / "周期对比测试"
    if test_dir.exists():
        import shutil
        shutil.rmtree(test_dir)
        print(f"\n🧹 测试数据已清理: {test_dir}")

if __name__ == "__main__":
    try:
        success = test_period_comparison()
        
        if success:
            print("\n🎉 所有测试通过！周期对比功能正常工作。")
        else:
            print("\n❌ 测试失败，请检查代码。")
            
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        cleanup_test_data()
