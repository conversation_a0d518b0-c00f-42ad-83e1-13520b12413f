#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试周期对比功能
==================

直接测试周期对比计算逻辑
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_period_comparison():
    """测试周期对比计算逻辑"""
    print("🔄 测试周期对比计算逻辑")
    
    # 创建测试数据
    start_time = datetime.now() - timedelta(hours=10)
    time_points = []
    equity_values = []
    
    # 生成10个小时的数据点
    base_equity = 1.0
    for i in range(10):
        current_time = start_time + timedelta(hours=i)
        time_points.append(current_time)
        
        # 模拟净值变化：总体上升但有波动
        trend = 0.001 * i  # 上升趋势
        noise = np.random.normal(0, 0.001)  # 随机波动
        equity_value = base_equity * (1 + trend + noise)
        equity_values.append(equity_value)
    
    # 创建DataFrame
    df = pd.DataFrame({
        'candle_begin_time': time_points,
        '净值': equity_values
    })
    
    # 重新计算净值，从1开始
    initial_value = df['净值'].iloc[0]
    df['重置净值'] = df['净值'] / initial_value
    
    print(f"📊 测试数据:")
    print(f"数据点数: {len(df)}")
    print(f"时间范围: {df['candle_begin_time'].min()} 到 {df['candle_begin_time'].max()}")
    print(f"净值范围: {df['重置净值'].min():.4f} 到 {df['重置净值'].max():.4f}")
    
    # 测试周期对比计算
    if len(df) < 2:
        print("❌ 数据点数不足，无法进行对比")
        return False
    
    # 获取最新数据点（当前周期）
    current_data = df.iloc[-1].copy()
    current_equity = current_data['重置净值']
    current_time = current_data['candle_begin_time']
    
    # 获取倒数第二个数据点（上一周期）
    previous_data = df.iloc[-2].copy()
    previous_equity = previous_data['重置净值']
    previous_time = previous_data['candle_begin_time']
    
    # 计算当前周期的收益率（相对于初始值1.0）
    current_total_return = (current_equity - 1.0) * 100
    
    # 计算上一周期的收益率（相对于初始值1.0）
    previous_total_return = (previous_equity - 1.0) * 100
    
    # 计算收益率变化
    return_change = current_total_return - previous_total_return
    
    # 计算当前回撤（需要重新计算到最新数据点的回撤）
    df_current = df.copy()
    df_current['净值_cummax'] = df_current['重置净值'].cummax()
    df_current['drawdown'] = (df_current['重置净值'] - df_current['净值_cummax']) / df_current['净值_cummax']
    
    current_drawdown = abs(df_current['drawdown'].iloc[-1]) * 100
    
    # 计算上一周期的回撤（到倒数第二个数据点）
    df_previous = df.iloc[:-1].copy()
    df_previous['净值_cummax'] = df_previous['重置净值'].cummax()
    df_previous['drawdown'] = (df_previous['重置净值'] - df_previous['净值_cummax']) / df_previous['净值_cummax']
    
    previous_drawdown = abs(df_previous['drawdown'].iloc[-1]) * 100
    
    # 计算回撤变化
    drawdown_change = current_drawdown - previous_drawdown
    
    # 计算时间间隔
    time_interval = current_time - previous_time
    time_interval_hours = time_interval.total_seconds() / 3600
    
    # 输出结果
    print(f"\n📈 当前周期:")
    print(f"  时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  总收益率: {current_total_return:.2f}%")
    print(f"  当前回撤: {current_drawdown:.2f}%")
    print(f"  净值: {current_equity:.4f}")
    
    print(f"\n📉 上一周期:")
    print(f"  时间: {previous_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  总收益率: {previous_total_return:.2f}%")
    print(f"  当前回撤: {previous_drawdown:.2f}%")
    print(f"  净值: {previous_equity:.4f}")
    
    print(f"\n🔄 变化情况:")
    print(f"  收益率变化: {return_change:+.2f}%")
    print(f"  回撤变化: {drawdown_change:+.2f}%")
    print(f"  时间间隔: {time_interval_hours:.1f}小时")
    
    # 生成趋势描述
    return_trend = "上升" if return_change > 0 else "下降" if return_change < 0 else "持平"
    drawdown_trend = "增加" if drawdown_change > 0 else "减少" if drawdown_change < 0 else "持平"
    
    print(f"  收益率趋势: {return_trend}")
    print(f"  回撤趋势: {drawdown_trend}")
    
    # 测试图表描述格式
    print(f"\n📈 资金曲线图描述格式:")
    strategy_name = "测试策略"
    equity_description = f"📈 **{strategy_name} - 资金曲线图**\n\n" + \
                       f"总收益率: {current_total_return:.2f}%\n" + \
                       f"年化收益率: {current_total_return * 365 / 10:.2f}%\n" + \
                       f"最大回撤: {current_drawdown:.2f}%"
    
    if return_change != 0:
        change_symbol = "📈" if return_change > 0 else "📉"
        equity_description += f"\n\n**与上一周期对比:**\n" + \
                            f"{change_symbol} 收益率变化: {return_change:+.2f}% ({return_trend})"
    
    print(equity_description)
    
    print(f"\n📉 回撤分析图描述格式:")
    drawdown_description = f"📉 **{strategy_name} - 回撤分析图**\n\n" + \
                         f"最大回撤: {current_drawdown:.2f}%\n" + \
                         f"当前回撤: {current_drawdown:.2f}%"
    
    if drawdown_change != 0:
        change_symbol = "⚠️" if drawdown_change > 0 else "✅"
        drawdown_description += f"\n\n**与上一周期对比:**\n" + \
                               f"{change_symbol} 回撤变化: {drawdown_change:+.2f}% ({drawdown_trend})"
    
    print(drawdown_description)
    
    print("\n✅ 周期对比功能测试通过！")
    return True

if __name__ == "__main__":
    try:
        print("="*50)
        print("开始测试周期对比功能")
        print("="*50)
        
        success = test_period_comparison()
        
        if success:
            print("\n🎉 所有测试通过！周期对比功能正常工作。")
        else:
            print("\n❌ 测试失败，请检查代码。")
            
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
